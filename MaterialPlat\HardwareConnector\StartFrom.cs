//using IHardware;
using System.Diagnostics;
using IHardware;
using static Logging.CCSSLogger;
using CmdLine;
using CommandLine;
using CCSS.HWConnector;
using Microsoft.VisualBasic;
using System.Windows.Forms;

namespace HardwareConnectorWinForm
{


    public partial class StartFrom : Form
    {
        private readonly Hw.IHardware? hardware;
        public StartFrom(string[] args)
        {
            InitializeComponent();
            this.WindowState = FormWindowState.Minimized;
            var opts = ParseOptions(args);
            KillMeIfParendDies(opts);

            //实际的注册需要在界面的主线程中
            hardware = HardWareConnection.GetHardWare(opts.Dir!);
            Task.Run(() =>
            {
                // 启动数据服务器
                HWDataClient.Start(opts.Data!, opts.Name!);
                // 启动命令服务器并设置命令处理函数
                HwCmdClient.Start(opts.Cmd!,
                  (msg) =>
                  {

                      /*
                       * this.InvokeRequired 是一个在 Windows Forms 应用程序中用于检查当前代码是否正在从 UI 线程之外的线程运行的属性。
                       * 它是 Control 类的一个属性，用于确定是否需要通过 Invoke 方法来调用 UI 线程，
                       * 以避免直接从非 UI 线程访问 UI 控件，这可能会导致线程安全问题。
                       * 它检查当前执行的线程是否是创建控件的线程（UI 线程）。如果不是，返回 true；如果是，返回 false。
                       */
                      if (this.InvokeRequired)
                      {
                          // Invoke 同步执行：在UI线程上同步执行代码，确保UI的状态一致性。
                          //如果不在UI线程上执行OpenDevice,多利控制器则没有数据回调
                          this.Invoke(new Action(() => HardWareConnection.handlerCmdMsg(hardware, msg)));
                      }
                      else
                      {
                          HardWareConnection.handlerCmdMsg(hardware, msg);
                      }
                  }, opts.Name!);
                // 启动Web服务器（OWIN 自托管 Web API 2 + Swagger）
                HardwareConnectorWinForm.Web.WebRuntime.HardwareInstance = hardware!;
                HardwareConnectorWinForm.Web.WebRuntime.DllName = opts.Name!;
                HardwareConnectorWinForm.Web.WebRuntime.Ports = opts.Ports!;
                Microsoft.Owin.Hosting.WebApp.Start<HardwareConnectorWinForm.Web.Startup>($"http://localhost:{opts.Http}");
                Console.WriteLine($"HardwareConnector Web API 已启动: http://localhost:{opts.Http}，Swagger: http://localhost:{opts.Http}/swagger");
            }
             );
            Logger.Info($"HardWareConnectorWinForm界面已经启动~");
            //界面隐藏
            this.Hide();
            //window底部工具隐藏小图标
            this.ShowInTaskbar = false;
        }


        void KillMeIfParendDies(Options opts)
        {
            int parentProcessId = opts.parentId;
            Logger.Info($"opts.parentId: {opts.parentId}");
            if (0 != parentProcessId)
            {
                Logger.Info($"监听TaskServer进程: {parentProcessId}");
                _ = Task.Run(() =>
                {
                    while (true)
                    {
                        try
                        {
                            var parentProcess = Process.GetProcessById(parentProcessId);
                            if (parentProcess.HasExited)
                            {
                                CloseAllDevice();
                                Logger.Warn("发现父进程结束, 硬件管理器结束");
                                Logger.Error("发现父进程结束, 硬件管理器结束");
                                Environment.Exit(1);
                            }

                        }
                        catch (ArgumentException)
                        {
                            CloseAllDevice();
                            Logger.Error("发现父进程结束, 硬件管理器结束");
                            Logger.Warn("异常中发现父进程结束, 硬件管理器结束");
                            Environment.Exit(1);
                        }
                        Thread.Sleep(1000);
                    }
                });
            }
        }
        /// <summary>
        /// 关闭所有的该dll的所有的物理硬件
        /// </summary>
        private void CloseAllDevice()
        {
            // 获取物理硬件
            var devices = hardware?.HwInfo.HwDevice;
            if (devices != null)
            {
                foreach (var device in devices.mySubHw)
                {
                    int res=  hardware!.CcssCloseDeviceID(device.SubID);
                    Logger.Error("device.SubID："+device.SubID+"关闭硬件设备返回值："+res);
                   
                }
            }
        }

        // 解析命令行参数并返回一个Options对象
        Options ParseOptions(string[] args)
        {
            Options? opt = new();
            var result = Parser.Default.ParseArguments<Options>(args).WithParsed<Options>(o =>
            {
                if (!string.IsNullOrEmpty(o.Dir))
                    Logger.Info($"DLL所在目录 {o.Dir}");
                else
                    throw new ArgumentNullException("没有指定DLL目录");
                if (0 != o.Http)
                    Logger.Info($"http port {o.Http}.");
                else
                    throw new ArgumentNullException("HTTP port 未指定");
                if (!string.IsNullOrEmpty(o.Cmd))
                    Logger.Info($"CMD port {o.Cmd}.");
                else
                    throw new ArgumentNullException("CMD port 未指定");
                if (!string.IsNullOrEmpty(o.Data))
                    Logger.Info($"Data port {o.Data}.");
                else
                    throw new ArgumentNullException("Data port 未指定");
                if (!string.IsNullOrEmpty(o.Name))
                    Logger.Info($"Dll name {o.Name}.");
                else
                    throw new ArgumentNullException("Dll name 未指定");

                opt = o;
            });
            return opt;
        }

    }

}

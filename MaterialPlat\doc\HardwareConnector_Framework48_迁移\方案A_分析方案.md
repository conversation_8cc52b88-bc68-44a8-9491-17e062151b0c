# 【分析方案】方案A：HardwareConnector 全量迁移至 .NET Framework 4.8（单进程装载）

## 一、前置条件确认（已确认）
- 业务约束：必须“直接替换即生效”，即 HardwareConnector 单进程装载客户 .NET Framework 4.8 版本的 HwSim 与 IHardware.dll（位于 C:\Users\<USER>\Desktop\HwSim16-framework4.8 参考）。

## 二、实施阶段划分
1) 阶段1：接口与库对齐（IHardware/HwSim）
   - 目标：IHardware 提供 net48 目标（可选同时提供 netstandard2.0），HwSim16 提供 net48 目标，与客户接口完全一致，命名空间与类型签名对齐。
   - 产出：
     - IHardware.csproj 支持 net48；
     - HwSim16.csproj 支持 net48；
     - 对齐文档：接口差异清单与规整方案；

2) 阶段2：HardwareConnector 项目迁移
   - 目标：将 HardwareConnector 从 net6.0-windows 迁移到 net48。
   - 子任务：
     - WinForms 启动：替换 ApplicationConfiguration.Initialize → 经典 WinForms 启动（Application.EnableVisualStyles 等）。
     - 依赖替换：
       - ASP.NET Core Minimal API → Web API 2（OWIN 自托管）或 HttpListener（推荐 Web API 2 以便保留 Swagger 与路由模型）。
       - Swashbuckle.AspNetCore → Swashbuckle.Core（Web API 2 版）。
       - System.IO.Ports 8.0 → 使用 .NET Framework 自带 System.IO.Ports。
       - 评估 NLog、NetMQ、System.Reactive、Newtonsoft.Json 的 net48 支持与版本锁定。
     - ApiServer 重写：保持 /api/* 路由、参数与返回结构一致；支持 CORS、静态文件、Swagger。

3) 阶段3：构建、运行与路径适配
   - 目标：生成 net48 产物，Electron/subTasks.json exePath 指向 net48 可执行；
   - 子任务：
     - 修改脚本与发布配置；
     - 验证“替换客户 DLL 即生效”（动态加载路径 Config.GetHardware）。

4) 阶段4：联调与回归
   - 覆盖 /api/* 路由、硬件数据通道、命令通道、异常处理、CORS、静态文件；
   - 客户提供 DLL 的实际替换回归；

## 三、可能遇到的问题
- Web API 2 自托管（OWIN）环境下，静态文件与 CORS、Swagger 组合配置复杂度较 ASP.NET Core 略高；
- 部分 NuGet 依赖需降级版本（例如 NLog、System.Reactive），需要逐一适配；
- ApiServer.cs 大量使用 System.Text.Json 与 Newtonsoft.Json 混合，需要在 Web API 2 中按需调整（默认 Newtonsoft.Json）；
- 串口 System.IO.Ports 的行为差异（若使用过新 API 需回退）。

## 四、优势与风险
- 优势：
  - 满足“直接替换即生效”的核心诉求；
  - 单进程架构简单，客户部署流程最清晰；
- 风险：
  - Web 层从 Minimal API 重写到 Web API 2，改造工作量大；
  - 需对齐客户 IHardware/HwSim 的接口定义（命名空间/类名/方法签名），否则无法无缝替换；

## 五、所需信息/依赖
- 客户源码 C:\Users\<USER>\Desktop\HwSim16-framework4.8 实际结构、IHardware.dll 的接口定义（若可提供 DLL 或接口头文件更佳）；
- 现网是否需要 Swagger UI；
- Electron 与脚本是否能切换到 net48 的可执行路径；

## 六、进度规划（初稿）
- P1（1~2 天）：IHardware/HwSim16 多目标（含 net48）与接口对齐清单；
- P2（3~5 天）：HardwareConnector Web 层迁移到 Web API 2（含 Swagger/CORS/静态文件）；
- P3（1~2 天）：WinForms 启动替换与依赖调优；
- P4（2~3 天）：联调与回归，客户 DLL 替换验证；

## 七、文档产出

- 《接口回归检查清单》：/api/* 行为在 Web API 2 的对应实现；


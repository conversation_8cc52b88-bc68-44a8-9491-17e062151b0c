using System.Web.Http;

namespace HardwareConnectorWinForm.Controllers
{
    [RoutePrefix("swagger/v1")]
    public class SwaggerJsonController : ApiController
    {
        // GET /swagger/v1/swagger.json → 重定向到 Web API 2 默认的 /swagger/docs/v1
        [HttpGet]
        [Route("swagger.json")]
        public IHttpActionResult RedirectToSwaggerDocs()
        {
            return Redirect("/swagger/docs/v1");
        }
    }
}


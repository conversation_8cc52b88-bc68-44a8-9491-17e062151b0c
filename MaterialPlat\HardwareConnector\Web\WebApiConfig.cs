using System.Net.Http.Formatting;
using System.Web.Http;
using System.Web.Http.Cors;
using Newtonsoft.Json.Serialization;

namespace HardwareConnectorWinForm.Web
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            // CORS
            config.EnableCors(new EnableCorsAttribute("*", "*", "*"));

            // 路由（优先 AttributeRouting）
            config.MapHttpAttributeRoutes();

            config.Routes.MapHttpRoute(
                name: "DefaultApi",
                routeTemplate: "api/{controller}/{action}"
            );

            // JSON 输出小驼峰
            var json = config.Formatters.JsonFormatter;
            json.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
            json.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Include;

            // 移除 XML
            config.Formatters.Remove(config.Formatters.XmlFormatter);

            // 确保使用 JsonMediaTypeFormatter
            //config.Formatters.Clear();
            //config.Formatters.Add(new JsonMediaTypeFormatter());

            // 错误详细信息（调试阶段）
            config.IncludeErrorDetailPolicy = IncludeErrorDetailPolicy.Always;
        }
    }
}


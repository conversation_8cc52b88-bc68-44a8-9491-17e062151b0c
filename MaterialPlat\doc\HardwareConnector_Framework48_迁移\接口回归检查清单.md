# 接口回归检查清单（HardwareConnector .NET Framework 4.8）

目标：确保与原 ASP.NET Core Minimal API 对外行为一致，Swagger 测试页 /swagger 可用，Swagger JSON 提供兼容路径。

## 环境准备
- 启动：HardwareConnector/bin/Debug/net48/HardwareConnector.exe
- 访问：
  - Swagger UI: http://localhost:{port}/swagger
  - Swagger JSON（兼容）：
    - http://localhost:{port}/swagger/v1/swagger.json（重定向）
    - http://localhost:{port}/swagger/docs/v1（默认）

## 用例列表

1) GET /api/portList
- 预期：返回启动参数中的 ports 字符串
- 校验：非空；与传入参数一致

2) GET /api/hardwareInfo
- 预期：返回 JSON 字符串，结构为 { key, ccssInfo: [HwInfo] }，属性小驼峰
- 校验：key 与 DllName 相同；ccssInfo[0] 字段与硬件实例 HwInfo 对齐

3) POST /api/setHardwareInfo
- 请求体：{"json":"{...合法HwInfoJson...}"}
- 预期：返回 ToResultMsg 结构，code=0，msg=success；同时 HwInfo 中各 Count 被更新
- 校验：再次调用 /api/hardwareInfo 时，count 字段符合请求体统计

4) GET /api/debugControl?isActive=1&interval=10&printMode=11&printIndex=-1
- 预期：返回 ToResultMsg 结构，data 中包含设置后的 4 个参数
- 校验：/api/debugControl 再次调用返回的参数保持一致；EventHandlers 内部值也一致

5) POST /api/ManagerForm
- 请求体：{"Key":"xxx","Json":"{}","Operation":"op"}
- 预期：透传 hardware.ManagerForm 的结果字符串

6) POST /api/CcssWriteJson
- 请求体：{"FormKey":"k","G0_RootID":0,"G1_HwType":0,"G2_AxisID":0,"G3_ADID":0,"G4_SensorID":0,"EventCode":"e","Json":"{}","flashFormFlag":0}
- 预期：返回 ToResultMsg 结构，code=0，data 为新的 json 字符串

7) POST /api/ExportTotxt
- 请求体：同上（Json 字段提供格式化内容）
- 预期：在 exportjson 下生成时间戳 txt，并自动用记事本打开；接口返回原始 Json

8) POST /api/openExe
- 请求体："C:\\Path\\To\\YourApp.exe"
- 预期：路径存在则“打开成功!”，否则“路径不存在!”

9) POST /api/dongle_add
- 请求体：{"ID":"","DongleID":"","Name":"","LeftCount":3,"ExpirationTime":"2025-12-31T23:59:59","Role":"","User":"","NumberOfComputers":1}
- 预期：按原 SafeDogUtils 流程成功返回 ToResultMsg(code=0)

10) POST /api/dongle_login
- 请求体：{"UserName":"u","RoleName":"r"}
- 预期：检测到安全狗返回 ToResultMsg(code=0)，否则 code!=0

## 注意事项
- 若 HwSim16 net48 构建需许可证编译（Licenses.licx），请使用 .NET Framework 版 MSBuild 构建。
- Swagger JSON 路径兼容：/swagger/v1/swagger.json（重定向）→ /swagger/docs/v1（默认）。
- 如果现网工具强依赖 /swagger/v1/swagger.json，可直接使用兼容路径。


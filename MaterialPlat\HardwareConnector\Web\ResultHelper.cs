using System.Text.Json;

namespace HardwareConnectorWinForm.Web
{
    /// <summary>
    /// 统一封装返回结果，保持与原 Minimal API 的 ToResultMsg 行为一致
    /// </summary>
    public static class ResultHelper
    {
        public static string ToResultMsg<T>(int code, string msg, T? t)
        {
            if (t == null)
            {
                var empty = JsonDocument.Parse("{}").RootElement;
                var res = new ReturnMsg(code, msg, empty);
                return System.Text.Json.JsonSerializer.Serialize(res);
            }
            string jsonString = System.Text.Json.JsonSerializer.Serialize(t);
            JsonDocument jsonDocument = JsonDocument.Parse(jsonString);
            JsonElement jsonElement = jsonDocument.RootElement;
            var result = new ReturnMsg(code, msg, jsonElement);
            return System.Text.Json.JsonSerializer.Serialize(result);
        }

        public record ReturnMsg(int code, string msg, JsonElement data);
    }
}


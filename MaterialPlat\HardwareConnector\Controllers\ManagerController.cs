using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Web.Http;
using HardwareConnectorWinForm.Web;
using Newtonsoft.Json;
using static Logging.CCSSLogger;
using static IHardware.Hw;

namespace HardwareConnectorWinForm.Controllers
{
    [RoutePrefix("api")]
    public class ManagerController : ApiController
    {
        public record ManagerForm(string Key, string Json, string Operation);
        public record WriteJson(string FormKey, int G0_RootID, int G1_HwType, int G2_AxisID, int G3_ADID, int G4_SensorID, string EventCode, string Json, int flashFormFlag);
        public record Dongle(string ID, string DongleID, string Name, ushort LeftCount, DateTime ExpirationTime, string Role, string User, byte NumberOfComputers);
        public record UserInfo(string UserName, string RoleName);

        // POST /api/ManagerForm
        [HttpPost]
        [Route("ManagerForm")]
        public IHttpActionResult ManagerFormPost([FromBody] ManagerForm item)
        {
            Logger.Error($"/api/ManagerForm 收到参数: {item.Operation}, Key: {item.Key}");
            var hw = WebRuntime.HardwareInstance;
            var result = hw.ManagerForm(item.Operation, item.Key, item.Json);
            Logger.Error($"/api/ManagerForm 执行完毕: {item.Operation}, Key: {item.Key}");
            return Ok(result);
        }

        // POST /api/CcssWriteJson
        [HttpPost]
        [Route("CcssWriteJson")]
        public IHttpActionResult CcssWriteJsonPost([FromBody] WriteJson item)
        {
            string json = item.Json;
            int flashFormFlag = item.flashFormFlag;
            var hw = WebRuntime.HardwareInstance;
            var ret = hw.CcssEditJson(item.FormKey, item.G0_RootID, item.G1_HwType, item.G2_AxisID, item.G3_ADID, item.G4_SensorID, item.EventCode, ref json, ref flashFormFlag);
            return Ok(ResultHelper.ToResultMsg(0, "success", json));
        }

        // POST /api/ExportTotxt
        [HttpPost]
        [Route("ExportTotxt")]
        public IHttpActionResult ExportToTxt([FromBody] WriteJson item)
        {
            string pageJson = item.Json;
            var jsonDocument = System.Text.Json.JsonDocument.Parse(pageJson);
            var options = new System.Text.Json.JsonSerializerOptions { WriteIndented = true };
            string formattedJson = System.Text.Json.JsonSerializer.Serialize(jsonDocument.RootElement, options);

            string txtName = DateTime.Now.ToString("yyyyMMddHHmmss") + ".txt";
            string filePath = Path.Combine(Directory.GetCurrentDirectory(), "exportjson\\" + txtName);
            string directoryPath = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directoryPath)) Directory.CreateDirectory(directoryPath);

            using (var writer = new StreamWriter(filePath, false, new UTF8Encoding(true)))
            {
                writer.Write(formattedJson);
            }
            Process.Start("notepad.exe", filePath);
            return Ok(pageJson);
        }

        // GET /api/debugControl
        [HttpGet]
        [Route("debugControl")]
        public IHttpActionResult DebugControl([FromUri] int isActive, [FromUri] int interval, [FromUri] int printMode, [FromUri] int printIndex)
        {
            try
            {
                if (isActive < 0 || isActive > 1)
                    return BadRequest(ResultHelper.ToResultMsg(-1, "fail", "isActive参数必须为0或1"));
                if (interval < 1)
                    return BadRequest(ResultHelper.ToResultMsg(-1, "fail", "interval参数必须大于等于1"));
                if (printMode < 0)
                    return BadRequest(ResultHelper.ToResultMsg(-1, "fail", "printMode参数必须大于等于0"));

                Events.EventHandlers.DebugIsActive = isActive;
                Events.EventHandlers.DebugInterval = interval;
                Events.EventHandlers.DebugPrintMode = printMode;
                Events.EventHandlers.DebugPrintIndex = printIndex;

                var result = new
                {
                    isActive = Events.EventHandlers.DebugIsActive,
                    interval = Events.EventHandlers.DebugInterval,
                    printMode = Events.EventHandlers.DebugPrintMode,
                    printIndex = Events.EventHandlers.DebugPrintIndex,
                    message = $"调试参数设置成功: isActive={isActive}, interval={interval}, printMode={printMode}, printIndex={printIndex}"
                };
                return Ok(ResultHelper.ToResultMsg(0, "success", result));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        // POST /api/openExe
        [HttpPost]
        [Route("openExe")]
        public IHttpActionResult OpenExe([FromBody] string path)
        {
            if (System.IO.File.Exists(path))
            {
                ProcessStartInfo startInfo = new ProcessStartInfo(path);
                Process.Start(startInfo);
                return Ok("打开成功!");
            }
            return Ok("路径不存在!");
        }

        // POST /api/dongle_add
        [HttpPost]
        [Route("dongle_add")]
        public IHttpActionResult DongleAdd([FromBody] Dongle dongle)
        {
            int ret = FuncLibs.SafeDogUtils.InitOrEditDog("", "test1");
            if (ret != 0) return Ok(ResultHelper.ToResultMsg(ret, "fail", "安全狗初始化失败"));

            string dongleID = FuncLibs.SafeDogUtils.GetChipID();
            ret = FuncLibs.SafeDogUtils.AuthNumber(dongle.LeftCount);
            if (ret != 0) return Ok(ResultHelper.ToResultMsg(ret, "fail", "安全狗添加使用次数失败"));
            ret = FuncLibs.SafeDogUtils.EditTime(dongle.ExpirationTime);
            if (ret != 0) return Ok(ResultHelper.ToResultMsg(ret, "fail", "安全狗添加使用时间失败"));
            ret = FuncLibs.SafeDogUtils.EditBind(dongle.NumberOfComputers);
            if (ret != 0) return Ok(ResultHelper.ToResultMsg(ret, "fail", "安全狗添加绑定电脑数失败"));

            ret = FuncLibs.SafeDogUtils.Write28k(System.Text.Json.JsonSerializer.Serialize(dongle));
            if (ret != 0) return Ok(ResultHelper.ToResultMsg(ret, "fail", "安全狗存储客户有关信息失败"));

            return Ok(ResultHelper.ToResultMsg(0, "success", ""));
        }

        // POST /api/dongle_login
        [HttpPost]
        [Route("dongle_login")]
        public IHttpActionResult DongleLogin([FromBody] UserInfo user)
        {
            // SafeDogUtils 验证逻辑按原实现保留（当前返回 success）
            FuncLibs.SafeDogUtils.VerfCode = "3DD640DF8E969555";
            int ret = FuncLibs.SafeDogUtils.DoesItExist();
            if (ret != 0)
            {
                return Ok(ResultHelper.ToResultMsg(ret, "fail", "未能检测到安全狗"));
            }
            return Ok(ResultHelper.ToResultMsg(0, "success", "{}".Trim()));
        }
    }
}


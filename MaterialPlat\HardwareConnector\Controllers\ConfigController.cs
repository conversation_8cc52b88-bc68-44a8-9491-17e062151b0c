using System;
using System.Web.Http;
using IHardware;
using Newtonsoft.Json;
using static IHardware.Hw;
using HardwareConnectorWinForm.Web;

namespace HardwareConnectorWinForm.Controllers
{
    [RoutePrefix("api")]
    public class ConfigController : ApiController
    {
        public record HwInfo(string json);

        // GET /api/hardwareInfo
        [HttpGet]
        [Route("hardwareInfo")]
        public IHttpActionResult GetHardwareInfo()
        {
            try
            {
                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
                };
                var items = new CcssInfo[] { WebRuntime.HardwareInstance.HwInfo };
                var json = JsonConvert.SerializeObject(new { key = WebRuntime.DllName, ccssInfo = items }, settings);
                return Ok(json);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        // POST /api/setHardwareInfo
        [HttpPost]
        [Route("setHardwareInfo")]
        public IHttpActionResult SetHardwareInfo([FromBody] HwInfo hwInfo)
        {
            try
            {
                var ccssInfoNew = JsonConvert.DeserializeObject<CcssInfo>(hwInfo.json);
                if (ccssInfoNew == null)
                {
                    return Content(System.Net.HttpStatusCode.BadRequest, ToResultMsg(-1, "fail", "反序列化失败"));
                }

                // 兼容原逻辑：统计 count 值
                var jsonDocument = System.Text.Json.JsonDocument.Parse(hwInfo.json);
                var root = jsonDocument.RootElement;
                int GetLen(string name)
                {
                    return root.TryGetProperty(name, out var arr) && arr.ValueKind == System.Text.Json.JsonValueKind.Array
                        ? arr.GetArrayLength() : 0;
                }
                ccssInfoNew.SubCount = root.GetProperty("hwDevice").GetProperty("mySubHw").GetArrayLength();
                ccssInfoNew.ServoAxisCount = GetLen("servoAxisSensor");
                ccssInfoNew.TempAxisCount = GetLen("tempAxisSensor");
                ccssInfoNew.CreepAxisCount = GetLen("creepAxisSensor");
                ccssInfoNew.ADCount = GetLen("myAD");
                ccssInfoNew.DACount = GetLen("myDA");
                ccssInfoNew.InputCount = GetLen("myInput");
                ccssInfoNew.OutputCount = GetLen("myOutput");
                ccssInfoNew.HandBoxCount = GetLen("myHandbox");

                WebRuntime.HardwareInstance.HwInfo = ccssInfoNew;
                return Ok(ToResultMsg(0, "success", "硬件信息更新完成！"));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        private static string ToResultMsg<T>(int code, string msg, T t)
        {
            var jsonElement = System.Text.Json.JsonSerializer.SerializeToElement(t);
            var result = new { code, msg, data = jsonElement };
            return System.Text.Json.JsonSerializer.Serialize(result);
        }
    }
}


# 【分析问题】HardwareConnector 迁移至 .NET Framework 4.8 分析报告

## 一、背景与目标
- 客户侧 HwSim16 源码为 .NET Framework 4.8，客户会自行编译 DLL 并直接替换我们打包版本中的 HwSim16.dll。
- 为保证 DLL 运行时装载兼容性，HardwareConnector 需要能够在 .NET Framework 4.8 环境下运行并加载客户提供的 .NET Framework 4.8 版 HwSim16.dll。
- 目标：在不影响现有业务能力的前提下，使 HardwareConnector 支持（优先）.NET Framework 4.8 运行，确保与客户侧 DLL 的“即插即用”。

- 客户提供的 HwSim16 .NET Framework 4.8 源码路径：C:\Users\<USER>\Desktop\HwSim16-framework4.8（需比对接口定义/命名空间/类型签名与当前 IHardware/HwSim16 的一致性）。


## 二、现状扫描（基于当前仓库）
- HardwareConnector（WinForms 可执行程序）
  - 目标框架：net6.0-windows
  - 关键依赖：
    - Microsoft.AspNetCore Minimal API（ApiServer.cs 使用 WebApplication/Kestrel/Swagger）
    - Swashbuckle.AspNetCore（6.5.0）
    - NetMQ、Newtonsoft.Json、NLog 5.1.1、System.Reactive 5.0、System.IO.Ports 8.0、CommandLineParser 2.9.1
  - 关键实现：
    - Program/StartFrom：WinForms 启动，使用 ApplicationConfiguration.Initialize（.NET 6 专有）
    - ApiServer.cs：Minimal API + Kestrel + Swagger UI 提供 HTTP 接口（/api/*），跨域、静态文件、若干 JSON 序列化逻辑（Newtonsoft + System.Text.Json 混用）
    - 通过 HardWareConnection/HwCmdClient/HWDataClient 与硬件交互
  - 项目引用：
    - 引用 HwSim16（类库，net6.0）
    - 引用 IHardware（接口，net6.0）
- HwSim16（库）
  - 目标框架：net6.0
  - 引用 IHardware（net6.0）
- IHardware（接口库）
  - 目标框架：net6.0

## 三、与 .NET Framework 4.8 的主要不兼容点
1) ASP.NET Core Minimal API/Kestrel/Swashbuckle.AspNetCore
   - .NET Framework 4.8 无法直接使用 Microsoft.AspNetCore.* Minimal API/Kestrel。
   - Swagger 需要改用 Web API 2 生态（Swashbuckle.Core for WebApi2）。
2) WinForms 启动模型
   - .NET 6 的 ApplicationConfiguration.Initialize 在 .NET Framework 4.8 中不可用，需要退回 Application.EnableVisualStyles 等经典写法。
3) System.IO.Ports
   - 当前 NuGet 包 8.0 仅面向 .NET（Core/5+/6+）。在 .NET Framework 4.8 下应使用框架内置的 System.IO.Ports（无需 NuGet）。
4) 接口与库的目标框架
   - 当前 IHardware 与 HwSim16 均为 net6.0。若 HardwareConnector 迁至 net48，则无法直接引用 net6.0 的 IHardware/HwSim16。
   - 需将 IHardware（以及可能的 HwSim16）提供 net48（或 netstandard2.0）目标，以实现共享接口与装载。
5) C# 语言特性
   - 文件作用域命名空间等 C# 10 特性在 .NET Framework 项目中可通过 LangVersion 支持，但建议回退到兼容写法以降低编译差异风险。

## 四、风险与影响评估
- 重写 Web 层：Minimal API → Web API 2（OWIN 自托管）或 HttpListener 实现，涉及所有 /api/* 路由、Swagger 文档生成、跨域、静态文件等替换与回归测试。
- 依赖替换：
  - Swashbuckle.AspNetCore → Swashbuckle.Core（适配 Web API 2）
  - System.IO.Ports 8.0 → 使用框架自带命名空间
  - 可能需调整 System.Reactive/NLog/NetMQ 的目标框架版本（均有 .NET Framework 支持，但需确认最低版本与 API 兼容性）
- 接口层兼容：IHardware 必须提供 .NET Framework 4.8 兼容目标（建议多目标 net48 + net6.0 或采用 netstandard2.0）。
- 打包与启动：Electron 侧硬编码路径、脚本（subTasks.json 中 exePath）需切换至 net48 产物目录。
- 维护成本：若保留双栈（net6 & net48），需建立 CI 构建矩阵与多目标条件编译，维护成本上升。

## 五、可选技术路线（1~3 个）
1) 方案A：全量迁移到 .NET Framework 4.8（单进程）
   - 做法：
     - HardwareConnector 由 net6 → net48（WinForms + OWIN 自托管 Web API 2 + Swashbuckle.Core）
     - IHardware 提供 net48 目标（推荐多目标：net48 + net6.0 / 或 netstandard2.0）
     - HwSim16 提供 net48 目标（用于客户替换），内部自用可保留 net6 目标（多目标）
     - 替换 System.IO.Ports、调整启动与路由、保持接口兼容
   - 优点：
     - 单进程直接装载客户的 net48 DLL，路径与替换流程清晰
     - 最贴近客户期望的“直接替换 DLL”模式
   - 缺点：
     - Web 层改造量较大；需要大量回归测试
     - 放弃 ASP.NET Core（需要 Web API 2 技术栈）

2) 方案B：双进程桥接（保留 net6 HardwareConnector）
   - 做法：
     - 新增 net48 Loader/Adapter 进程：负责加载客户 HwSim16.dll（net48），通过 NetMQ/命名管道与现有 net6 HardwareConnector 进程通信
     - HardwareConnector 保持 net6，Minimal API/Swagger 不改
     - IHardware 合同可迁移为 netstandard2.0（供双方共用），或在进程边界进行消息协议映射
   - 优点：
     - 对现有 Web 与 UI 改动最小，落地快
   - 缺点：
     - 双进程部署与监控复杂度增加；IPC 需要处理健壮性与性能
     - 客户“替换 DLL”不再是直接替换 net6 进程的装载 DLL（需要明确替换位置与 Loader 目录）

3) 方案C：多目标+可切换发行包
   - 做法：
     - HardwareConnector 提供 net48 与 net6 双目标（两个可执行文件），根据客户场景选择发行；
     - IHardware/HwSim16 亦提供双目标；
     - Electron/脚本按需指向对应 exePath。
   - 优点：
     - 一套代码，多种部署；可平滑过渡
   - 缺点：
     - 维护成本较高；构建脚本与测试矩阵需完善

## 六、推荐方案（待用户确认）
- 若必须满足“客户直接替换 DLL、单进程装载”的明确诉求，优先推荐 方案A（全量迁移至 .NET Framework 4.8）。
- 若允许引入额外进程并优先降低改造量，推荐 方案B（双进程桥接）作为快速方案，后续再视情况过渡到 A 或 C。

## 七、待确认问题
1) 是否必须保持“单进程装载客户 DLL”？
2) 是否必须保留 Swagger UI？若保留，Web API 2 下可用 Swashbuckle.Core；
3) 客户开发环境 VS 版本与可用包管理器（是否可接受 netstandard2.0 作为接口合同）；
4) Electron/脚本（subTasks.json 中 exePath）是否可按环境切换（Debug/Release、net48 路径）；
5) 当前 /api/* 路由是否允许小幅调整（如模型绑定差异、返回格式差异）。

## 八、初步里程碑与工作量（估算）
- M1（1~2 天）：IHardware/HwSim16 多目标化（引入 net48 或 netstandard2.0），编译通过
- M2（3~5 天）：HardwareConnector Web 层改造（OWIN Web API 2 自托管、CORS、静态文件、Swagger、路由迁移）
- M3（1~2 天）：WinForms 启动与依赖替换（System.IO.Ports、NLog 配置、语言版本与编译兼容性）
- M4（2~3 天）：联调与回归测试（/api/* 对齐、硬件模拟/真机验证、Electron 集成）

## 结论
- 迁移到 .NET Framework 4.8 可行，但需要对 Web 层与依赖进行系统性替换；
- 建议先确认“单进程 vs 双进程”的约束，再据此选择方案 A 或 B/C；
- 本报告作为后续《分析方案》《细化方案》《执行方案》的基础文档。


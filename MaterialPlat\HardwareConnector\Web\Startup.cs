using System;
using System.Web.Http;
using Microsoft.Owin;
using Microsoft.Owin.Cors; // 需安装 Microsoft.Owin.Cors 包
using Microsoft.Owin.FileSystems;
using Microsoft.Owin.StaticFiles;
using Owin;
using Swashbuckle.Application;

[assembly: OwinStartup(typeof(HardwareConnectorWinForm.Web.Startup))]
namespace HardwareConnectorWinForm.Web
{
    /// <summary>
    /// OWIN 启动类：配置 WebApi、CORS、静态文件与 Swagger。
    /// </summary>
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            // CORS
            app.UseCors(CorsOptions.AllowAll);

            // // 静态文件（可选）: wwwroot 目录下静态资源
            // var fileSystem = new PhysicalFileSystem("wwwroot");
            // var options = new FileServerOptions
            // {
            //     EnableDefaultFiles = true,
            //     FileSystem = fileSystem
            // };
            // options.StaticFileOptions.ServeUnknownFileTypes = true;
            // app.UseFileServer(options);

            // WebApi 配置
            var config = new HttpConfiguration();
            WebApiConfig.Register(config);

            // Swagger: UI 路径固定为 /swagger
            config.EnableSwagger(c =>
            {
                c.SingleApiVersion("v1", "Hardware Connector API");
                // Web API 2 的 IncludeXmlComments 只有一个参数重载（路径），暂时关闭 XML 注释
            })
            .EnableSwaggerUi(c =>
            {
                c.DisableValidator();
                c.DocumentTitle("硬件连接器API文档");
                c.DocExpansion(DocExpansion.None);
                c.EnableDiscoveryUrlSelector();
                // Web API 2 版不支持 EnableDeepLinking
            });

            app.UseWebApi(config);
        }
    }
}


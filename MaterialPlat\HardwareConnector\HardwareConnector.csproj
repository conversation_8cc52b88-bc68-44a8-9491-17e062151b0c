﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net6.0-windows;net48</TargetFrameworks>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- PlatformTarget removed for cross-platform test execution -->
    <PlatformTarget>x86</PlatformTarget>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <LangVersion>10.0</LangVersion>
    <ImplicitUsings>true</ImplicitUsings>

    <RuntimeIdentifiers>win-x86</RuntimeIdentifiers>

  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.2.9" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Cors" Version="5.2.9" />
    <PackageReference Include="Microsoft.AspNet.WebApi.OwinSelfHost" Version="5.2.9" />
    <PackageReference Include="Microsoft.Owin" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.Cors" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.Host.HttpListener" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.Hosting" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.StaticFiles" Version="4.2.2" />
    <PackageReference Include="NetMQ" Version="********" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NLog" Version="5.1.1" />
    <PackageReference Include="Owin" Version="1.0.0" />
    <PackageReference Include="Swashbuckle.Core" Version="5.6.0" />
    <PackageReference Include="System.Reactive" Version="5.0.0" />
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net48' ">
    <None Include="Polyfills\IsExternalInit.cs" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="ApiServer.cs" />
  </ItemGroup>



  <ItemGroup>
    <ProjectReference Include="..\HardwareSim\Hwsim\HwSim16.csproj" />
    <ProjectReference Include="..\HardwareSim\IHardware\IHardware.csproj" />
    <ProjectReference Include="..\Logger\Logger.csproj" />
    <ProjectReference Include="..\FuncLibs\FuncLibs.csproj" />
  </ItemGroup>

</Project>
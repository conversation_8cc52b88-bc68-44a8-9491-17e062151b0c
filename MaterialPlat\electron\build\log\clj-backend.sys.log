2025-09-05 15:37:13,417 [main] INFO  clj-backend.core - -main running ~~ 
2025-09-05 15:37:13,630 [main] ERROR jdbc.audit - 1. Connection.prepareStatement(SELECT
*
FROM t_system_version
WHERE 1 = 1




LIMIT 1;
;) SELECT * FROM t_system_version WHERE 1 = 1 LIMIT 1; ; 
 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: t_system_version)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at hugsql.adapter.next_jdbc.HugsqlAdapterNextJdbc.query(next_jdbc.clj:20)
	at hugsql.adapter$fn__4770$G__4741__4775.invoke(adapter.clj:3)
	at hugsql.adapter$fn__4770$G__4740__4781.invoke(adapter.clj:3)
	at clojure.lang.Var.invoke(Var.java:401)
	at hugsql.core$db_fn_STAR_$y__5039.doInvoke(core.clj:472)
	at clojure.lang.RestFn.invoke(RestFn.java:448)
	at hugsql.core$db_fn_STAR_$y__5039.invoke(core.clj:462)
	at conman.core$try_query$fn__6913$fn__6914.invoke(core.clj:32)
	at clj_backend.common.common_db$fn__7527$f__6933__auto____7554.invoke(common_db.clj:6)
	at clj_backend.common.db_utils$fn__8989$fn__8990.invoke(db_utils.clj:248)
	at clj_backend.common.db_utils$fn__8989.invokeStatic(db_utils.clj:247)
	at clj_backend.common.db_utils$fn__8989.invoke(db_utils.clj:244)
	at mount.core$record_BANG_.invokeStatic(core.cljc:74)
	at mount.core$record_BANG_.invoke(core.cljc:73)
	at mount.core$up$fn__7243.invoke(core.cljc:81)
	at mount.core$up.invokeStatic(core.cljc:80)
	at mount.core$up.invoke(core.cljc:78)
	at mount.core$bring.invokeStatic(core.cljc:247)
	at mount.core$bring.invoke(core.cljc:239)
	at mount.core$start.invokeStatic(core.cljc:289)
	at mount.core$start.doInvoke(core.cljc:281)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clojure.core$apply.invokeStatic(core.clj:667)
	at clojure.core$apply.invoke(core.clj:662)
	at mount.core$start_with_args.invokeStatic(core.cljc:388)
	at mount.core$start_with_args.doInvoke(core.cljc:385)
	at clojure.lang.RestFn.invoke(RestFn.java:463)
	at clj_backend.core$_main.invokeStatic(core.clj:161)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
2025-09-05 15:37:13,632 [main] ERROR jdbc.sqltiming - 1. Connection.prepareStatement(SELECT
*
FROM t_system_version
WHERE 1 = 1




LIMIT 1;
;) FAILED! SELECT * FROM t_system_version WHERE 1 = 1 LIMIT 1; ; 
 {FAILED after -1 msec} 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: t_system_version)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at hugsql.adapter.next_jdbc.HugsqlAdapterNextJdbc.query(next_jdbc.clj:20)
	at hugsql.adapter$fn__4770$G__4741__4775.invoke(adapter.clj:3)
	at hugsql.adapter$fn__4770$G__4740__4781.invoke(adapter.clj:3)
	at clojure.lang.Var.invoke(Var.java:401)
	at hugsql.core$db_fn_STAR_$y__5039.doInvoke(core.clj:472)
	at clojure.lang.RestFn.invoke(RestFn.java:448)
	at hugsql.core$db_fn_STAR_$y__5039.invoke(core.clj:462)
	at conman.core$try_query$fn__6913$fn__6914.invoke(core.clj:32)
	at clj_backend.common.common_db$fn__7527$f__6933__auto____7554.invoke(common_db.clj:6)
	at clj_backend.common.db_utils$fn__8989$fn__8990.invoke(db_utils.clj:248)
	at clj_backend.common.db_utils$fn__8989.invokeStatic(db_utils.clj:247)
	at clj_backend.common.db_utils$fn__8989.invoke(db_utils.clj:244)
	at mount.core$record_BANG_.invokeStatic(core.cljc:74)
	at mount.core$record_BANG_.invoke(core.cljc:73)
	at mount.core$up$fn__7243.invoke(core.cljc:81)
	at mount.core$up.invokeStatic(core.cljc:80)
	at mount.core$up.invoke(core.cljc:78)
	at mount.core$bring.invokeStatic(core.cljc:247)
	at mount.core$bring.invoke(core.cljc:239)
	at mount.core$start.invokeStatic(core.cljc:289)
	at mount.core$start.doInvoke(core.cljc:281)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clojure.core$apply.invokeStatic(core.clj:667)
	at clojure.core$apply.invoke(core.clj:662)
	at mount.core$start_with_args.invokeStatic(core.cljc:388)
	at mount.core$start_with_args.doInvoke(core.cljc:385)
	at clojure.lang.RestFn.invoke(RestFn.java:463)
	at clj_backend.core$_main.invokeStatic(core.clj:161)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
2025-09-05 15:37:13,946 [main] INFO  migratus.core - Starting migrations 
2025-09-05 15:37:13,951 [main] ERROR jdbc.audit - 2. Connection.prepareStatement(SELECT 1 FROM schema_migrations) SELECT 1 FROM schema_migrations 
 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: schema_migrations)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at next.jdbc.sql$query.invokeStatic(sql.clj:104)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at next.jdbc.sql$query.invokeStatic(sql.clj:101)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at migratus.database$table_exists_QMARK_.invokeStatic(database.clj:189)
	at migratus.database$table_exists_QMARK_.invoke(database.clj:177)
	at migratus.database$init_schema_BANG_.invokeStatic(database.clj:247)
	at migratus.database$init_schema_BANG_.invoke(database.clj:239)
	at migratus.database.Database.connect(database.clj:312)
	at migratus.core$run.invokeStatic(core.clj:53)
	at migratus.core$run.invoke(core.clj:50)
	at migratus.core$reset.invokeStatic(core.clj:167)
	at migratus.core$reset.invoke(core.clj:163)
	at luminus_migrations.core$fn__8879.invokeStatic(core.clj:28)
	at luminus_migrations.core$fn__8879.invoke(core.clj:27)
	at luminus_migrations.core$migrate.invokeStatic(core.clj:98)
	at luminus_migrations.core$migrate.invoke(core.clj:84)
	at clj_backend.core$_main.invokeStatic(core.clj:168)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
2025-09-05 15:37:13,951 [main] ERROR jdbc.sqltiming - 2. Connection.prepareStatement(SELECT 1 FROM schema_migrations) FAILED! SELECT 1 FROM schema_migrations 
 {FAILED after -1 msec} 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: schema_migrations)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at next.jdbc.sql$query.invokeStatic(sql.clj:104)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at next.jdbc.sql$query.invokeStatic(sql.clj:101)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at migratus.database$table_exists_QMARK_.invokeStatic(database.clj:189)
	at migratus.database$table_exists_QMARK_.invoke(database.clj:177)
	at migratus.database$init_schema_BANG_.invokeStatic(database.clj:247)
	at migratus.database$init_schema_BANG_.invoke(database.clj:239)
	at migratus.database.Database.connect(database.clj:312)
	at migratus.core$run.invokeStatic(core.clj:53)
	at migratus.core$run.invoke(core.clj:50)
	at migratus.core$reset.invokeStatic(core.clj:167)
	at migratus.core$reset.invoke(core.clj:163)
	at luminus_migrations.core$fn__8879.invokeStatic(core.clj:28)
	at luminus_migrations.core$fn__8879.invoke(core.clj:27)
	at luminus_migrations.core$migrate.invokeStatic(core.clj:98)
	at luminus_migrations.core$migrate.invoke(core.clj:84)
	at clj_backend.core$_main.invokeStatic(core.clj:168)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
2025-09-05 15:37:13,952 [main] INFO  migratus.database - creating migration table 'schema_migrations' 
2025-09-05 15:37:14,027 [main] WARN  migratus.migrations - skipping: '29990101000000-add-subtask-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-09-05 15:37:14,032 [main] WARN  migratus.migrations - skipping: '29990101000001-add-sample-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-09-05 15:37:14,117 [main] INFO  migratus.core - Ending migrations 
2025-09-05 15:37:14,120 [main] INFO  migratus.core - Starting migrations 
2025-09-05 15:37:14,168 [main] WARN  migratus.migrations - skipping: '29990101000000-add-subtask-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-09-05 15:37:14,171 [main] WARN  migratus.migrations - skipping: '29990101000001-add-sample-init-data.up.sql - 快捷方式.lnk' migrations must match pattern: ^(\d+)-([^\.]+)((?:\.[^\.]+)+)$ 
2025-09-05 15:37:14,229 [main] INFO  migratus.core - Running up for [20230222081921 20230224024126 20230227115525 20230301080547 20230309054138 20230316060040 20230324070914 20230421090947 20230509013720 20230531064426 20230615090452 20230627085851 20231018032209 20231111055444 20240317141418 20240318031213 20240328015431 20240407022048 20240409025215 20240418023711 20241029011329 20241109033358 20241114094737 20241210033352 20241225024444 20250101000000 20250103022555 20250110010951 20250213032140 20250213100203 20250214102123 20250217074603 20250217075221 20250225034224 20250304062051 20250304075728 20250312075213 20250321030015 20250325023407 20250519103156 29990101000000 29990101000001] 
2025-09-05 15:37:14,231 [main] INFO  migratus.core - Up 20230222081921-add-unit-tables 
2025-09-05 15:37:14,234 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,262 [main] INFO  migratus.core - Up 20230224024126-add-international-table 
2025-09-05 15:37:14,263 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,268 [main] INFO  migratus.core - Up 20230227115525-add-sample-tables 
2025-09-05 15:37:14,268 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,274 [main] INFO  migratus.core - Up 20230301080547-add-binder-tables 
2025-09-05 15:37:14,274 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,279 [main] INFO  migratus.core - Up 20230309054138-add-function-table 
2025-09-05 15:37:14,279 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,283 [main] INFO  migratus.core - Up 20230316060040-add-template-table 
2025-09-05 15:37:14,284 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,288 [main] INFO  migratus.core - Up 20230324070914-add-hardware-tables 
2025-09-05 15:37:14,288 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,295 [main] INFO  migratus.core - Up 20230421090947-add-user-tables 
2025-09-05 15:37:14,296 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,312 [main] INFO  migratus.core - Up 20230509013720-add-internal-code-table 
2025-09-05 15:37:14,313 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,317 [main] INFO  migratus.core - Up 20230531064426-add-picture-table 
2025-09-05 15:37:14,317 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,335 [main] INFO  migratus.core - Up 20230615090452-add-project-table 
2025-09-05 15:37:14,335 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,339 [main] INFO  migratus.core - Up 20230627085851-add-module-datasource-table 
2025-09-05 15:37:14,339 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,343 [main] INFO  migratus.core - Up 20231018032209-add-audio-table 
2025-09-05 15:37:14,344 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,348 [main] INFO  migratus.core - Up 20231111055444-add-station 
2025-09-05 15:37:14,348 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,353 [main] INFO  migratus.core - Up 20240317141418-add-system-version-table 
2025-09-05 15:37:14,354 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,358 [main] INFO  migratus.core - Up 20240318031213-add-log 
2025-09-05 15:37:14,358 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,361 [main] INFO  migratus.core - Up 20240328015431-add-col-sample-parameter 
2025-09-05 15:37:14,361 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,366 [main] INFO  migratus.core - Up 20240407022048-add-station-project 
2025-09-05 15:37:14,366 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,369 [main] INFO  migratus.core - Up 20240409025215-add-field-state-project-station 
2025-09-05 15:37:14,371 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,375 [main] INFO  migratus.core - Up 20240418023711-add-field-default-cfg-station 
2025-09-05 15:37:14,375 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,380 [main] INFO  migratus.core - Up 20241029011329-create-system-time-table 
2025-09-05 15:37:14,380 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,383 [main] INFO  migratus.core - Up 20241109033358-init-audio-data 
2025-09-05 15:37:14,384 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,394 [main] INFO  migratus.core - Up 20241114094737-add-project-temporary-flag 
2025-09-05 15:37:14,395 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,399 [main] INFO  migratus.core - Up 20241210033352-add-hardware-ccss-fields 
2025-09-05 15:37:14,400 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,419 [main] INFO  migratus.core - Up 20241225024444-add-control-library-tables 
2025-09-05 15:37:14,419 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,425 [main] INFO  migratus.core - Up 20250101000000-add-hardware-id-mapping 
2025-09-05 15:37:14,425 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,429 [main] INFO  migratus.core - Up 20250103022555-update-hardware-axis-device-table-fields 
2025-09-05 15:37:14,429 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,435 [main] INFO  migratus.core - Up 20250110010951-create-inspection-tables 
2025-09-05 15:37:14,435 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,441 [main] INFO  migratus.core - Up 20250213032140-create-station-or-home-layout-config-table 
2025-09-05 15:37:14,441 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,445 [main] INFO  migratus.core - Up 20250213100203-add-field-station-project-table 
2025-09-05 15:37:14,446 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,449 [main] INFO  migratus.core - Up 20250214102123-update-station-or-home-layout-config-datas 
2025-09-05 15:37:14,449 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,455 [main] INFO  migratus.core - Up 20250217074603-add-field-guide-dialog 
2025-09-05 15:37:14,455 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,462 [main] INFO  migratus.core - Up 20250217075221-add-field-input-variable 
2025-09-05 15:37:14,462 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,467 [main] INFO  migratus.core - Up 20250225034224-add-system-config 
2025-09-05 15:37:14,468 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,471 [main] INFO  migratus.core - Up 20250304062051-update-inspection-table 
2025-09-05 15:37:14,472 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,495 [main] INFO  migratus.core - Up 20250304075728-add-sample-parameter-col 
2025-09-05 15:37:14,495 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,499 [main] INFO  migratus.core - Up 20250312075213-add-feild-project 
2025-09-05 15:37:14,499 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,506 [main] INFO  migratus.core - Up 20250321030015-add-feild-description 
2025-09-05 15:37:14,506 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,510 [main] INFO  migratus.core - Up 20250325023407-add-field-sample-param 
2025-09-05 15:37:14,510 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,515 [main] INFO  migratus.core - Up 20250519103156-add-input-variable-boolean-tab 
2025-09-05 15:37:14,515 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,518 [main] INFO  migratus.core - Up 29990101000000-add-subtask-init-data 
2025-09-05 15:37:14,519 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,616 [main] INFO  migratus.core - Up 29990101000001-add-sample-init-data 
2025-09-05 15:37:14,616 [main] INFO  migratus.database - Connection is  {:connection #object[net.sf.log4jdbc.ConnectionSpy 0x5f851718 net.sf.log4jdbc.ConnectionSpy@5f851718]} Config is {:store :database, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,641 [main] INFO  migratus.core - Ending migrations 
2025-09-05 15:37:14,642 [main] INFO  migratus.core - Starting migrations 
2025-09-05 15:37:14,645 [main] INFO  migratus.database - creating migration table 'schema_migrations' 
2025-09-05 15:37:14,748 [main] INFO  migratus.core - Ending migrations 
2025-09-05 15:37:14,750 [main] INFO  migratus.core - Starting migrations 
2025-09-05 15:37:14,839 [main] INFO  migratus.core - Running up for [20230222081921 20230227115525 20230301080547 20230303030427 20230306073201 20230308031706 20230308061505 20230309054138 20230310060839 20230311074534 20230313074247 20230315020002 20230315030952 20230324070914 20230426092633 20230505065622 20230509013720 20230614060018 20230620055337 20230815065225 20230816083119 20230908015613 20230912032834 20231018032209 20231207021356 20231207023335 20240104163312 20240123084214 20240308070540 20240309061427 20240313090755 20240319020246 20240325082816 20240327071526 20240327123609 20240401030938 20240408082118 20240422034811 20240509121358 20240516072246 20240730055736 20240819101526 20240823024703 20241011015022 20241014054607 20241105102712 20241108092239 20241119020534 20241226030758 20241231021048 20250113083200 20250115012653 20250117101718 20250120072613 20250121031956 20250121060754 20250208034153 20250211033310 20250211093937 20250212085342 20250219093040 20250221023540 20250224035012 20250227091306 20250303075133 20250304075812 20250306060336 20250306081753 20250312013359 20250312075000 20250317085159 20250321024314 20250325023426 20250417011824 20250427053604 20250516031245 20250627061857 20250702061611 20250801024809 20250803121942 20250806012558] 
2025-09-05 15:37:14,841 [main] INFO  migratus.core - Up 20230222081921-add-unit-tables 
2025-09-05 15:37:14,841 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,851 [main] INFO  migratus.core - Up 20230227115525-add-sample-tables 
2025-09-05 15:37:14,851 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,858 [main] INFO  migratus.core - Up 20230301080547-add-binder-tables 
2025-09-05 15:37:14,858 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,867 [main] INFO  migratus.core - Up 20230303030427-add-guide-tables 
2025-09-05 15:37:14,868 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,876 [main] INFO  migratus.core - Up 20230306073201-add-result-variable-table 
2025-09-05 15:37:14,876 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,883 [main] INFO  migratus.core - Up 20230308031706-add-signal-variable-table 
2025-09-05 15:37:14,884 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,891 [main] INFO  migratus.core - Up 20230308061505-add-input-variable-table 
2025-09-05 15:37:14,891 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,901 [main] INFO  migratus.core - Up 20230309054138-add-function-table 
2025-09-05 15:37:14,902 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,909 [main] INFO  migratus.core - Up 20230310060839-add-passage-form-table 
2025-09-05 15:37:14,910 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,916 [main] INFO  migratus.core - Up 20230311074534-add-shortcut-table 
2025-09-05 15:37:14,917 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,924 [main] INFO  migratus.core - Up 20230313074247-add-simple-instance 
2025-09-05 15:37:14,924 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,930 [main] INFO  migratus.core - Up 20230315020002-add-flow-chart-data 
2025-09-05 15:37:14,932 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,938 [main] INFO  migratus.core - Up 20230315030952-add-sample-instance-about 
2025-09-05 15:37:14,939 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,946 [main] INFO  migratus.core - Up 20230324070914-add-hardware-tables 
2025-09-05 15:37:14,947 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,953 [main] INFO  migratus.core - Up 20230426092633-add-scheduler-context-col 
2025-09-05 15:37:14,953 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,961 [main] INFO  migratus.core - Up 20230505065622-teat-migrate 
2025-09-05 15:37:14,961 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,967 [main] INFO  migratus.core - Up 20230509013720-add-internal-code-table 
2025-09-05 15:37:14,967 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,975 [main] INFO  migratus.core - Up 20230614060018-add-control-library 
2025-09-05 15:37:14,975 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,982 [main] INFO  migratus.core - Up 20230620055337-add-t-action-table 
2025-09-05 15:37:14,982 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,990 [main] INFO  migratus.core - Up 20230815065225-add-t-static-curve-table 
2025-09-05 15:37:14,991 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:14,998 [main] INFO  migratus.core - Up 20230816083119-table-config 
2025-09-05 15:37:14,999 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,006 [main] INFO  migratus.core - Up 20230908015613-add-page-table 
2025-09-05 15:37:15,006 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,015 [main] INFO  migratus.core - Up 20230912032834-add-daq 
2025-09-05 15:37:15,015 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,022 [main] INFO  migratus.core - Up 20231018032209-add-video-table 
2025-09-05 15:37:15,022 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,030 [main] INFO  migratus.core - Up 20231207021356-add-export-table 
2025-09-05 15:37:15,030 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,038 [main] INFO  migratus.core - Up 20231207023335-add-sample-variable-table 
2025-09-05 15:37:15,038 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,046 [main] INFO  migratus.core - Up 20240104163312-add-dongtai-config 
2025-09-05 15:37:15,046 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,053 [main] INFO  migratus.core - Up 20240123084214-add-subtask-table 
2025-09-05 15:37:15,053 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,061 [main] INFO  migratus.core - Up 20240308070540-add-test-result 
2025-09-05 15:37:15,061 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,068 [main] INFO  migratus.core - Up 20240309061427-add-dynamic-curve 
2025-09-05 15:37:15,068 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,075 [main] INFO  migratus.core - Up 20240313090755-add-action-table-primary-key 
2025-09-05 15:37:15,076 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,087 [main] INFO  migratus.core - Up 20240319020246-add-col-t-passage-form 
2025-09-05 15:37:15,088 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,096 [main] INFO  migratus.core - Up 20240325082816-add-col-t-signal-variable 
2025-09-05 15:37:15,096 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,102 [main] INFO  migratus.core - Up 20240327071526-add-col-sample-parameter 
2025-09-05 15:37:15,103 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,109 [main] INFO  migratus.core - Up 20240327123609-add-page-window-property 
2025-09-05 15:37:15,109 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,117 [main] INFO  migratus.core - Up 20240401030938-add-field-t-signal-variable 
2025-09-05 15:37:15,118 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,124 [main] INFO  migratus.core - Up 20240408082118-add-field-t-action 
2025-09-05 15:37:15,126 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,133 [main] INFO  migratus.core - Up 20240422034811-add-field-t-mapping 
2025-09-05 15:37:15,133 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,139 [main] INFO  migratus.core - Up 20240509121358-add-widget-pid 
2025-09-05 15:37:15,139 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,160 [main] INFO  migratus.core - Up 20240516072246-add-report-table-csv-cols 
2025-09-05 15:37:15,161 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,171 [main] INFO  migratus.core - Up 20240730055736-add-auxiliary-line 
2025-09-05 15:37:15,172 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,179 [main] INFO  migratus.core - Up 20240819101526-add-sample-instance-update-time 
2025-09-05 15:37:15,179 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,188 [main] INFO  migratus.core - Up 20240823024703-add-shortcut-col-tip 
2025-09-05 15:37:15,188 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,195 [main] INFO  migratus.core - Up 20241011015022-add-curve-marking-cols 
2025-09-05 15:37:15,196 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,213 [main] INFO  migratus.core - Up 20241014054607-add-virtual-signal-var-reset-cols 
2025-09-05 15:37:15,213 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,219 [main] INFO  migratus.core - Up 20241105102712-add-col-shortcut 
2025-09-05 15:37:15,221 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,228 [main] INFO  migratus.core - Up 20241108092239-add-table-col 
2025-09-05 15:37:15,228 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,235 [main] INFO  migratus.core - Up 20241119020534-add-sample-inst-checked 
2025-09-05 15:37:15,235 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,243 [main] INFO  migratus.core - Up 20241226030758-add-control-library-table-version-col 
2025-09-05 15:37:15,244 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,255 [main] INFO  migratus.core - Up 20241231021048-add-global-project-mapping-table 
2025-09-05 15:37:15,255 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,262 [main] INFO  migratus.core - Up 20250113083200-add-field-dialog-binder 
2025-09-05 15:37:15,262 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,276 [main] INFO  migratus.core - Up 20250115012653-add-field-input-variable 
2025-09-05 15:37:15,277 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,285 [main] INFO  migratus.core - Up 20250117101718-add-instal-widget 
2025-09-05 15:37:15,286 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,293 [main] INFO  migratus.core - Up 20250120072613-add-double-array-curve-table 
2025-09-05 15:37:15,293 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,301 [main] INFO  migratus.core - Up 20250121031956-add-feild-widget 
2025-09-05 15:37:15,302 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,308 [main] INFO  migratus.core - Up 20250121060754-add-action-execution-timing-field 
2025-09-05 15:37:15,309 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,317 [main] INFO  migratus.core - Up 20250208034153-add-feild-widget-binder 
2025-09-05 15:37:15,317 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,325 [main] INFO  migratus.core - Up 20250211033310-add-auxiliary-line-fields 
2025-09-05 15:37:15,325 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,333 [main] INFO  migratus.core - Up 20250211093937-add-static-curve-point-count 
2025-09-05 15:37:15,333 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,341 [main] INFO  migratus.core - Up 20250212085342-add-widget-data-source-table 
2025-09-05 15:37:15,342 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,349 [main] INFO  migratus.core - Up 20250219093040-add-widght-data 
2025-09-05 15:37:15,349 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,355 [main] INFO  migratus.core - Up 20250221023540-add-feild-action 
2025-09-05 15:37:15,356 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,365 [main] INFO  migratus.core - Up 20250224035012-add-dynamic-curve 
2025-09-05 15:37:15,366 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,379 [main] INFO  migratus.core - Up 20250227091306-update-widget-data 
2025-09-05 15:37:15,379 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,386 [main] INFO  migratus.core - Up 20250303075133-add-widght-data 
2025-09-05 15:37:15,386 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,392 [main] INFO  migratus.core - Up 20250304075812-add-sample-parameter-col 
2025-09-05 15:37:15,392 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,401 [main] INFO  migratus.core - Up 20250306060336-add-widght-data 
2025-09-05 15:37:15,401 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,408 [main] INFO  migratus.core - Up 20250306081753-update-widght-data 
2025-09-05 15:37:15,408 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,414 [main] INFO  migratus.core - Up 20250312013359-add-widght-data 
2025-09-05 15:37:15,415 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,421 [main] INFO  migratus.core - Up 20250312075000-add-template-comparison-signature-table 
2025-09-05 15:37:15,421 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,427 [main] INFO  migratus.core - Up 20250317085159-add-widght-data 
2025-09-05 15:37:15,428 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,434 [main] INFO  migratus.core - Up 20250321024314-add-feild-description 
2025-09-05 15:37:15,435 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,444 [main] INFO  migratus.core - Up 20250325023426-add-field-sample-param 
2025-09-05 15:37:15,444 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,456 [main] INFO  migratus.core - Up 20250417011824-add-static-curve-col 
2025-09-05 15:37:15,456 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,466 [main] INFO  migratus.core - Up 20250427053604-add-widght 
2025-09-05 15:37:15,466 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,473 [main] INFO  migratus.core - Up 20250516031245-add-input-variable-boolean-tab 
2025-09-05 15:37:15,473 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,481 [main] INFO  migratus.core - Up 20250627061857-add-static-curve-col 
2025-09-05 15:37:15,481 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,489 [main] INFO  migratus.core - Up 20250702061611-add-navbar-login-check 
2025-09-05 15:37:15,489 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,498 [main] INFO  migratus.core - Up 20250801024809-add-atom-comp 
2025-09-05 15:37:15,498 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,508 [main] INFO  migratus.core - Up 20250803121942-add-binder-is-lock 
2025-09-05 15:37:15,509 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,516 [main] INFO  migratus.core - Up 20250806012558-add-export-excel-double-array 
2025-09-05 15:37:15,516 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x3a95fd1c org.sqlite.jdbc4.JDBC4Connection@3a95fd1c]} Config is {:store :database, :migration-dir migrations_template, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,526 [main] INFO  migratus.core - Ending migrations 
2025-09-05 15:37:15,527 [main] INFO  migratus.core - Starting migrations 
2025-09-05 15:37:15,529 [main] INFO  migratus.database - creating migration table 'schema_migrations' 
2025-09-05 15:37:15,605 [main] INFO  migratus.core - Ending migrations 
2025-09-05 15:37:15,605 [main] INFO  migratus.core - Starting migrations 
2025-09-05 15:37:15,677 [main] INFO  migratus.core - Running up for [20230222081921 20240325093726 20240515084445 20240812031107 20250228022223 20250303013738 20250403023041] 
2025-09-05 15:37:15,677 [main] INFO  migratus.core - Up 20230222081921-add-table 
2025-09-05 15:37:15,678 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6fc294ae org.sqlite.jdbc4.JDBC4Connection@6fc294ae]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,686 [main] INFO  migratus.core - Up 20240325093726-add-messages-daq-copy-table 
2025-09-05 15:37:15,686 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6fc294ae org.sqlite.jdbc4.JDBC4Connection@6fc294ae]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,694 [main] INFO  migratus.core - Up 20240515084445-add-daqs-table 
2025-09-05 15:37:15,694 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6fc294ae org.sqlite.jdbc4.JDBC4Connection@6fc294ae]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,701 [main] INFO  migratus.core - Up 20240812031107-drop-daq-tables 
2025-09-05 15:37:15,701 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6fc294ae org.sqlite.jdbc4.JDBC4Connection@6fc294ae]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,707 [main] INFO  migratus.core - Up 20250228022223-add-creep-tf-special-point 
2025-09-05 15:37:15,708 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6fc294ae org.sqlite.jdbc4.JDBC4Connection@6fc294ae]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,714 [main] INFO  migratus.core - Up 20250303013738-add-double-array-save-table 
2025-09-05 15:37:15,715 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6fc294ae org.sqlite.jdbc4.JDBC4Connection@6fc294ae]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,722 [main] INFO  migratus.core - Up 20250403023041-add-log-control-table 
2025-09-05 15:37:15,722 [main] INFO  migratus.database - Connection is  {:connection #object[org.sqlite.jdbc4.JDBC4Connection 0x6fc294ae org.sqlite.jdbc4.JDBC4Connection@6fc294ae]} Config is {:store :database, :migration-dir migrations_data, :db {:connection-uri uri-censored}} 
2025-09-05 15:37:15,728 [main] INFO  migratus.core - Ending migrations 

2025-09-05 09:02:39,874 [XNIO-1 task-9] ERROR io.undertow.request - UT005071: Undertow request failed HttpServerExchange{ GET /api/picture/images/3db096b7-cbbb-4415-ad14-135216f9bc71} 
java.lang.IllegalStateException: UT000196: Session with id NZ05eyeHY3VOwIIiRTFtOeYQyNr_8pNhg4eb8J9T already exists
	at io.undertow.server.session.InMemorySessionManager.createSession(InMemorySessionManager.java:179)
	at io.undertow.util.Sessions.getSession(Sessions.java:60)
	at io.undertow.util.Sessions.getOrCreateSession(Sessions.java:49)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invokeStatic(session.clj:64)
	at ring.adapter.undertow.middleware.session$get_or_create_session.invoke(session.clj:54)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:90)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:178)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-05 09:32:39,880 [XNIO-1 task-4] ERROR org.jboss.threads.errors - Thread Thread[XNIO-1 task-4,5,main] threw an uncaught exception 
java.lang.NullPointerException: null
2025-09-05 13:58:26,260 [XNIO-1 task-4] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:52)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:37)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.common.http_client$post.invokeStatic(http_client.clj:25)
	at clj_backend.common.http_client$post.invoke(http_client.clj:22)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invokeStatic(service.clj:536)
	at clj_backend.modules.hardware.station.service$host_instance_handle.invoke(service.clj:522)
	at clj_backend.modules.sys.user.sys_user_service$login.invokeStatic(sys_user_service.clj:96)
	at clj_backend.modules.sys.user.sys_user_service$login.invoke(sys_user_service.clj:91)
	at clj_backend.modules.sys.user.sys_user_routes$routes$fn__43149.invoke(sys_user_routes.clj:56)
	at clj_backend.common.trial$trial_middleware$fn__42698.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52552$fn__52554$fn__52555.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52575$fn__52577$fn__52578.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49531$fn__49532.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52770.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52693.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52697.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52690.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.adapter.undertow.middleware.session$wrap_undertow_session$wrapper_fn__18290.invoke(session.clj:88)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18384.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18442.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18740.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18764.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18059.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18935.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18800.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18883.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18907.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18864.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18324.invoke(x_headers.clj:22)
	at ring.adapter.undertow$undertow_handler$fn$reify__58450.handleRequest(undertow.clj:40)
	at io.undertow.server.session.SessionAttachmentHandler.handleRequest(SessionAttachmentHandler.java:68)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:387)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
